<template>
    <alert-content :on-default-save="submit">
        <n-descriptions
            label-placement="left"
            label-align="right"
            label-style="width: 100px;"
            bordered
            :column="2"
            size="small"
        >
            <n-descriptions-item label="文件名称" :span="2"> {{ data.documentName }} </n-descriptions-item>
            <n-descriptions-item label="文件有效性"> {{ data.documentValidityLabel }} </n-descriptions-item>
            <n-descriptions-item label="文件类型"> {{ data.documentModuleName }} </n-descriptions-item>
            <n-descriptions-item label="文件类别"> {{ data.documentCategoryName }} </n-descriptions-item>
            <n-descriptions-item label="文件编号"> {{ data.documentNo }} </n-descriptions-item>
            <n-descriptions-item label="版本/版次"> {{ data.documentVersionNo }} </n-descriptions-item>
        </n-descriptions>
        <n-form
            ref="formRef"
            class="mt-15px"
            :model="form"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="form.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                        :is-date-disabled="disableDate"
                    />
                </n-form-item-gi>

                <n-form-item-gi :span="12" label="借阅原因" path="reason">
                    <n-select
                        v-model:value="form.reason"
                        :options="$datas.borrowing.reasonOptions"
                        placeholder="请选择借阅原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="form.reason === 5" :span="24" label="其他原因" path="otherReason">
                    <n-input
                        v-model:value="form.otherReason"
                        maxlength="50"
                        placeholder="请输入其他借阅原因"
                        show-count
                    />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import { FormRules } from 'naive-ui';

const store = useStore();

const props = defineProps<{
    row: any;
    type: number; // 2-内部文件 3-外部文件
}>();

const formRef = ref();

const disableDate = (ts: number) => {
    // 禁用过去的日期，当前日期及之后的日期可点击
    return ts < dayjs().startOf('day').valueOf();
};
const data = computed(() => {
    return {
        documentId: props.row.id,
        documentName: props.row.name,
        documentValidity: props.row.status,
        documentValidityLabel: $datas.borrowing.documentValidityOptions.find((item) => item.value === props.row.status)
            ?.label,
        documentModuleType: props.type,
        documentModuleName: props.type === 2 ? '内部文件' : '外部文件',
        documentCategoryName: props.row.docCategoryName || props.row.docType,
        documentCategoryId: props.row.docCategoryId || props.row.typeDictionaryNodeId,
        documentNo: props.row.no || props.row.number,
        documentVersionNo: props.row.versionNo || props.row.version
    };
});
const rules: FormRules = {
    borrowPeriod: { required: true, message: '请输入借阅日期', trigger: ['blur', 'change'], type: 'array' },
    reason: { required: true, message: '请选择借阅原因', trigger: ['blur', 'change'], type: 'number' },
    otherReason: { required: true, message: '请输入其他借阅原因', trigger: ['blur', 'change'] }
};

const form = ref({
    borrowPeriod: null as [number, number] | null,
    reason: null as number | null,
    otherReason: null as string | null
});

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    // 组装借阅业务表单数据
    const _data: any = {
        approvalStatus: -1,
        reason: form.value.reason,
        otherReason: form.value.reason === 5 ? form.value.otherReason : '',
        borrowTime: form.value.borrowPeriod?.[0],
        dueTime: form.value.borrowPeriod?.[1],
        documents: [
            {
                documentId: data.value.documentId,
                documentModuleType: data.value.documentModuleType,
                documentVersionNo: data.value.documentVersionNo
            }
        ]
    };
    let borrowRecordId;
    await $apis.nebula.api.v1.documentLibrary.loans.create(_data).then((res) => {
        borrowRecordId = res.data.id;
    });

    // 组装借阅审批数据
    const documents = [
        {
            documentValidity: data.value.documentValidity,
            documentModuleType: data.value.documentModuleType,
            documentCategoryId: data.value.documentCategoryName,
            documentId: data.value.documentName,
            documentNo: data.value.documentNo,
            documentVersionNo: data.value.documentVersionNo
        }
    ];
    const result = {
        userNickname: store.userInfo.nickname,
        approvalApplyTime: dayjs().format('YYYY-MM-DD'),
        borrowPeriod: form.value.borrowPeriod,
        reason: form.value.reason,
        otherReason: form.value.reason === 5 ? form.value.otherReason : '',
        documents
    };
    const formData = JSON.stringify({
        businessId: 'FILE_BORROW',
        version: '1.0.0',
        borrowRecordId,
        data: result
    });
    await $hooks.useApprovalProcess('FILE_BORROW', formData);
    window.$message.success('借阅申请已提交');
};
</script>

<style scoped></style>
