<template>
    <div class="external-file-library bg-#fff">
        <n-tabs v-model:value="activeTab" type="line" class="px-10px mb-5px" @update:value="handleTabChange">
            <n-tab v-if="store.permissions.indexOf('externalFileGroupTab') > -1" name="group">集团库</n-tab>
            <n-tab v-if="store.permissions.indexOf('externalFileCompanyTab') > -1" name="company">公司库</n-tab>
        </n-tabs>
        <div class="flex">
            <file-type-tree
                ref="fileTypeTreeRef"
                v-model="params.typeDictionaryNodeIds"
                type="external"
                @change="handleTreeChange"
            />
            <n-search-table-page
                v-if="activeTab !== ''"
                ref="searchTablePageRef"
                class="w-[calc(100%-220px)]"
                :data-table-props="{
                    columns,
                    size: 'small',
                    scrollX: 2600,
                    maxHeight: 'calc(100vh - 480px)',
                    rowKey: (row) => row.id,
                    checkedRowKeys: checkedRowKeys,
                    'onUpdate:checkedRowKeys': handleCheck
                }"
                :params="params"
                :data-api="$apis.nebula.api.v1.external.getList"
                :search-props="{
                    showAdd: store.permissions.indexOf('externalFileGroupAdd') > -1 && activeTab === 'group',
                    showInput: false
                }"
                :search-table-space="{ size: 20 }"
                :pagination-props="{
                    showQuickJumper: true,
                    showSizePicker: true,
                    pageSizes: [10, 15, 20, 30, 50, 100]
                }"
                :auto-load="false"
                @reset="handleReset"
                @add="handleAdd()"
            >
                <template #search_form_middle>
                    <n-input
                        class="w-168px!"
                        v-model:value="params.number"
                        clearable
                        :placeholder="activeTab === 'group' ? '请输入集团文件编号' : '请输入文件编号'"
                    />
                    <n-input class="w-168px!" v-model:value="params.name" clearable placeholder="输入文件名称" />
                </template>
                <template #search_handle_after>
                    <n-permission v-if="activeTab === 'group'" has="externalFileGroupBatchInclude">
                        <n-button type="primary" @click="handleBatchInclude">批量纳入</n-button>
                    </n-permission>
                    <n-permission v-if="activeTab === 'group'" has="externalFileGroupImport">
                        <n-button @click="handleImport">导入</n-button>
                    </n-permission>
                    <n-permission
                        v-if="
                            (activeTab === 'group' && store.permissions.indexOf('externalFileGroupExport') > -1) ||
                            (activeTab === 'company' && store.permissions.indexOf('externalFileCompanyExport') > -1)
                        "
                    >
                        <n-button type="warning" @click="handleExport">导出</n-button>
                    </n-permission>
                </template>
                <template #search_form_after>
                    <n-button type="primary" @click="showMore = !showMore">{{ showMore ? '收起' : '更多' }}</n-button>
                </template>
                <template #search_bottom_layout>
                    <n-collapse-transition :show="showMore" v-if="showMore">
                        <n-space>
                            <n-input
                                class="w-168px!"
                                v-model:value="params.originalNumber"
                                clearable
                                placeholder="输入原文件编号"
                            />
                            <n-input
                                class="w-168px!"
                                v-model:value="params.originalDocNumber"
                                clearable
                                placeholder="输入原文件号"
                            />
                            <n-select
                                class="w-140px"
                                v-model:value="params.domainDictionaryNodeId"
                                :options="domainOptions"
                                clearable
                                placeholder="选择所属领域"
                            />
                            <n-input
                                class="w-168px!"
                                v-model:value="params.publishDocNumber"
                                clearable
                                placeholder="输入发文号"
                            />
                            <n-input
                                class="w-168px!"
                                v-model:value="params.publishDepartment"
                                clearable
                                placeholder="输入发文部门"
                            />
                            <n-select
                                class="w-140px"
                                v-model:value="params.authenticationDictionaryNodeId"
                                :options="certTypeOptions"
                                clearable
                                placeholder="选择认证方式"
                            />
                            <n-select
                                class="w-140px"
                                v-model:value="params.beAttachedFile"
                                :options="$datas.fileLibrary.hasAttachmentOptions"
                                clearable
                                placeholder="是否有附件"
                            />
                            <n-select
                                class="w-140px"
                                v-model:value="params.status"
                                :options="statusOptions"
                                clearable
                                placeholder="选择状态"
                            />
                        </n-space>
                    </n-collapse-transition>
                </template>
                <template #table_publishDate="{ row }">
                    <n-time v-if="row.publishDate" :time="row.publishDate" format="yyyy-MM-dd" />
                </template>
                <template #table_effectiveDate="{ row }">
                    <n-time v-if="row.effectiveDate" :time="row.effectiveDate" format="yyyy-MM-dd" />
                </template>
                <template #table_status="{ row }">
                    <n-tag :bordered="false" round size="small" :type="$datas.fileLibrary.statusMap[row.status].type">
                        {{ $datas.fileLibrary.statusMap[row.status].label }}
                    </n-tag>
                </template>
                <template #table_approvalInfo_approvers="{ row }">
                    {{ formatApprovers(row.approvalInfo.approvers) }}
                    <span v-if="row.approvalInfo?.approvers.length === 0" class="text-gray-400"> - </span>
                </template>
                <template #table_approvalInfo_auditors="{ row }">
                    {{ formatApprovers(row.approvalInfo.auditors) }}
                    <span v-if="row.approvalInfo?.auditors.length === 0" class="text-gray-400"> - </span>
                </template>
                <template #table_todo="{ row }">
                    <n-space justify="center" :wrap="false">
                        <n-permission
                            v-if="
                                (activeTab === 'group' && store.permissions.indexOf('externalFileGroupView') > -1) ||
                                (activeTab === 'company' && store.permissions.indexOf('externalFileCompanyView') > -1)
                            "
                        >
                            <n-button size="tiny" @click="handleView(row)">查阅</n-button>
                        </n-permission>
                        <n-permission
                            v-if="
                                (activeTab === 'group' && store.permissions.indexOf('externalFileGroupBorrow') > -1) ||
                                (activeTab === 'company' && store.permissions.indexOf('externalFileCompanyBorrow') > -1)
                            "
                        >
                            <n-button size="tiny" type="primary" @click="handleBorrow(row)">借阅</n-button>
                        </n-permission>
                        <n-permission
                            v-if="
                                (activeTab === 'group' &&
                                    store.permissions.indexOf('externalFileGroupDownload') > -1) ||
                                (activeTab === 'company' &&
                                    store.permissions.indexOf('externalFileCompanyDownload') > -1)
                            "
                        >
                            <n-button size="tiny" type="primary" @click="handleDownload(row)">下载</n-button>
                        </n-permission>
                        <n-dropdown
                            v-if="todoOptions.length > 0"
                            trigger="click"
                            :options="todoOptions(row)"
                            @select="(key) => handleTodoMenu(key, row)"
                        >
                            <n-button size="tiny">更多</n-button>
                        </n-dropdown>
                    </n-space>
                </template>
            </n-search-table-page>
            <n-empty v-else description="暂无权限  无法访问" class="min-h-300px justify-center" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';

const store = useStore();

const activeTab = ref('');
const fileTypeTreeRef = ref();
const searchTablePageRef = ref();
const showMore = ref(false);
const params = ref<any>({
    page: 1, // 页码
    pageSize: 10, // 每页条数
    noPage: false, // 是否分页
    number: '', // 文件编号
    name: '', // 文件名称
    originalNumber: '', // 原文件编号
    originalDocNumber: '', // 原文件发文号
    publishDocNumber: '', // 发文号
    publishDepartment: '', // 发文部门
    typeDictionaryNodeIds: null, // 文件类型（数组）
    domainDictionaryNodeId: null, // 所属领域
    authenticationDictionaryNodeId: null, // 认证方式
    beAttachedFile: null, // 是否附件
    status: null, // 状态
    orgType: 1 // 组织类型
});

const columns = computed<DataTableColumns>(() => [
    {
        type: 'selection'
    },
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => `${index + 1}`,
        fixed: 'left'
    },
    {
        title: activeTab.value === 'group' ? '集团文件编号' : '文件编号',
        key: 'number',
        align: 'center',
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '版本/版次', key: 'version', align: 'center', width: 80, ellipsis: { tooltip: true }, resizable: true },
    { title: '原文件编号', key: 'originalNumber', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '原版本/版次',
        key: 'originalVersion',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '文件名称', key: 'name', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件英文名称', key: 'englishName', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件类别', key: 'docType', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '所属领域', key: 'domain', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '原文件号', key: 'originalDocNumber', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '发文号', key: 'publishDocNumber', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '发文部门', key: 'publishDepartment', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '替代文件',
        key: 'replacementDocName',
        align: 'center',
        width: 120,
        ellipsis: { tooltip: true },
        resizable: true
    },
    {
        title: '替代版本/版次',
        key: 'replacementDocVersion',
        align: 'center',
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '备注', key: 'remark', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '审核人',
        key: 'approvalInfo_approvers',
        align: 'center',
        width: 120,
        ellipsis: { tooltip: true },
        resizable: true
    },
    {
        title: '批准人',
        key: 'approvalInfo_auditors',
        align: 'center',
        width: 120,
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '发布日期', key: 'publishDate', align: 'center', width: 120, resizable: true },
    { title: '实施日期', key: 'effectiveDate', align: 'center', width: 120, resizable: true },
    { title: '认证方式', key: 'authentication', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '状态', key: 'status', align: 'center', width: 100, ellipsis: { tooltip: true }, resizable: true },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 220 }
]);

const formatApprovers = (approvers: any[]) => {
    return approvers.map((approver) => approver.userNickname).join(',');
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};
const handleReset = () => {
    params.value = {
        ...params.value,
        page: 1, // 页码
        pageSize: 10, // 每页条数
        noPage: false, // 是否分页
        number: '', // 文件编号
        name: '', // 文件名称
        originalNumber: '', // 原文件编号
        originalDocNumber: '', // 原文件发文号
        publishDocNumber: '', // 发文号
        publishDepartment: '', // 发文部门
        domainDictionaryNodeId: null, // 所属领域
        authenticationDictionaryNodeId: null, // 认证方式
        beAttachedFile: null, // 是否附件
        status: null // 状态
    };
    init();
};

// 存储选中的行数据
const checkedRows = ref<any[]>([]);
const checkedRowKeys = ref<string[]>([]);

const handleCheck = (checked: any) => {
    checkedRowKeys.value = checked;
    const data = searchTablePageRef.value?.tableRef;
    if (data) {
        const currentData = data.data || data.tableData || [];

        // 根据选中的key过滤出对应的行数据，并转换为批量纳入需要的格式
        const selectedData = currentData.filter((row: any) => checked.includes(row.id));
        checkedRows.value = selectedData.map((row: any) => ({
            id: row.id,
            fileNo: row.number,
            fileName: row.name,
            originalNumber: '', // 原文件编号直接为空
            originalVersion: '' // 原版本版次直接为空
        }));
    } else {
        checkedRows.value = [];
    }
};
const getDict = async () => {
    const code = await api.sass.api.v1.dict.get('file_business_dictionary');
    const [authentication_method_tree, field_of_expertise_tree] = await Promise.all([
        $apis.nebula.api.v1.businessDictionary.node.tree({ id: code.data.data[0].extra.authDictionaryId }),
        $apis.nebula.api.v1.businessDictionary.node.tree({ id: code.data.data[0].extra.domainDictionaryId })
    ]);

    certTypeOptions.value = $utils.treeData.convertTreeData(authentication_method_tree.data);
    domainOptions.value = $utils.treeData.convertTreeData(field_of_expertise_tree.data);
};

function openFileFormDialog({ title, row = null, readonly = false }: { title: string; row?: any; readonly?: boolean }) {
    $alert.dialog({
        title,
        content: import('./models/external-file-form.vue'),
        width: '800px',
        props: {
            row,
            bookLibraryOptions: bookLibraryOptions.value,
            domainOptions: domainOptions.value,
            certTypeOptions: certTypeOptions.value,
            orgType: params.value.orgType,
            readonly,
            onSave: () => handleReset()
        }
    });
}

const handleAdd = (row?: any) => {
    if (row) {
        openFileFormDialog({ title: '修订', row, readonly: false });
    } else {
        openFileFormDialog({ title: '新增', row: null, readonly: false });
    }
};
const handleBatchInclude = async () => {
    $alert.dialog({
        title: '批量纳入',
        content: import('./models/batch-inclusion.vue'),
        width: '1000px',
        props: {
            checkedRow: checkedRows.value,
            onSave: () => {
                handleReset();
                // 清空选中状态
                checkedRows.value = [];
                checkedRowKeys.value = [];
            }
        }
    });
};
const handleImport = () => {
    $alert.dialog({
        title: '导入',
        content: import('../models/ledger-file-import.vue'),
        width: '600px',
        props: {
            type: 'external',
            onSave: () => handleReset()
        }
    });
};
const handleExport = async () => {
    window.$dialog.warning({
        title: '提示',
        content: '确认后将导出该筛选条件下的数据，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.documentLibrary.index.export({
                moduleType: 3,
                params: params.value
            });
            window.$message.info('数据导出成功，请在右上角下拉菜单【数据导出】中查看');
        }
    });
};
const handleDetail = (row: any) => {
    openFileFormDialog({ title: '详情', row, readonly: true });
};
const handleDistribute = (row: any) => {
    $alert.dialog({
        title: '发放回收',
        content: import('../models/distribute-file.vue'),
        style: 'width: 90%; min-width: 1050px; overflow-x: auto;',
        props: {
            row: row,
            type: 'external',
            onSave: () => handleReset()
        }
    });
};
const handleInclude = (row: any) => {
    $alert.dialog({
        title: '纳入子公司',
        content: import('./models/inclusion-company.vue'),
        width: '800px',
        props: {
            row,
            onSave: () => handleReset()
        }
    });
};
const handleInvalid = (row: any) => {
    console.log('作废', row);
};

const handleBorrow = (row: any) => {
    $utils.filePermissionUtils.handleBorrow(row, 3);
};

const handleView = (row: any) => {
    $utils.filePermissionUtils.handleView(row, 2);
};

const handleDownload = (row: any) => {
    $utils.filePermissionUtils.handleDownload(row, 2);
};

const handleChangeLog = (row: any) => {
    $alert.dialog({
        title: '变更记录',
        content: import('../models/change-log.vue'),
        width: '80%',
        props: {
            id: row.id
        }
    });
};

// 处理树形组件变化
const handleTreeChange = (keys: string[]) => {
    params.value.typeDictionaryNodeIds = keys;
    init();
};

const domainOptions = ref<any[]>([]);
const certTypeOptions = ref<any[]>([]);
const bookLibraryOptions = computed(() => {
    return fileTypeTreeRef.value?.treeData[0].children;
});

const statusOptions = ref([
    { label: '即将作废', value: 1 },
    { label: '即将实施', value: 2 },
    { label: '有效', value: 3 },
    { label: '拟修订', value: 4 }
]);

const todoOptions = (row: any) => {
    const perms = store.permissions;
    const options = [];
    /**
     * TODO: 权限判断
     *
     * 【集团文件管理员】
     * 集团库Tab：新增、导入、导出、修订、详情、发放回收、作废、借阅、查阅、下载、变更记录
     *
     * 【集团普通用户】
     * 集团库Tab：借阅、查阅、下载
     *
     * 【子公司文件管理员】
     * 集团库Tab：批量纳入、纳入子公司、借阅、查阅、下载；
     * 公司库Tab：导出、详情、发放回收、作废、借阅、查阅、下载、变更记录；
     *
     * 【子公司普通用户】
     * 公司库Tab：借阅、查阅、下载
     */
    if (perms.includes('externalFileGroupRevision') && activeTab.value === 'group')
        options.push({ label: '修订', key: 'revision' });
    if (
        (perms.includes('externalFileGroupDetail') && activeTab.value === 'group') ||
        (perms.includes('externalFileCompanyDetail') && activeTab.value === 'company')
    )
        options.push({ label: '详情', key: 'detail' });
    if (perms.includes('externalFileGroupInclude') && activeTab.value === 'group')
        options.push({ label: '纳入子公司', key: 'include' });
    if (
        ((perms.includes('externalFileGroupDistribute') && activeTab.value === 'group') ||
            (perms.includes('externalFileCompanyDistribute') && activeTab.value === 'company')) &&
        row.status === 3
    )
        options.push({ label: '发放回收', key: 'distribute' });
    if (
        (perms.includes('externalFileGroupInvalid') && activeTab.value === 'group') ||
        (perms.includes('externalFileCompanyInvalid') && activeTab.value === 'company')
    )
        options.push({ label: '作废', key: 'invalid' });
    if (
        (perms.includes('externalFileGroupChangeLog') && activeTab.value === 'group') ||
        (perms.includes('externalFileCompanyChangeLog') && activeTab.value === 'company')
    )
        options.push({ label: '变更记录', key: 'changeLog' });

    return options;
};

const handleTodoMenu = (key: string, row: any) => {
    switch (key) {
        case 'revision':
            handleAdd(row);
            break;
        case 'detail':
            handleDetail(row);
            break;
        case 'distribute':
            handleDistribute(row);
            break;
        case 'include':
            handleInclude(row);
            break;
        case 'invalid':
            handleInvalid(row);
            break;
        case 'changeLog':
            handleChangeLog(row);
            break;
    }
};

const handleTabChange = (val: string) => {
    activeTab.value = val;
    if (val === 'group') params.value.orgType = 1;
    else if (val === 'company') params.value.orgType = 2;
    init();
};

onMounted(async () => {
    getDict();
    if (store.permissions.indexOf('externalFileGroupTab') > -1) {
        activeTab.value = 'group';
        params.value.orgType = 1;
    } else if (store.permissions.indexOf('externalFileCompanyTab') > -1) {
        activeTab.value = 'company';
        params.value.orgType = 2;
    }
});
</script>

<style scoped lang="less"></style>
