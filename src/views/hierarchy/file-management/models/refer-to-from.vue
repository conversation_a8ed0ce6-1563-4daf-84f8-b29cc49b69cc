<template>
    <alert-content :on-default-save="submit">
        <n-descriptions
            label-placement="left"
            label-align="right"
            label-style="width: 90px;"
            bordered
            :column="2"
            size="small"
        >
            <n-descriptions-item label="申请人"> {{ data.applicant }} </n-descriptions-item>
            <n-descriptions-item label="申请日期"> {{ data.applyDate }} </n-descriptions-item>
            <n-descriptions-item label="文件名称"> {{ data.name }} </n-descriptions-item>
            <n-descriptions-item label="文件类别"> {{ data.fileCategory }} </n-descriptions-item>
            <n-descriptions-item label="文件编号"> {{ data.number }} </n-descriptions-item>
            <n-descriptions-item label="版本/版次"> {{ data.versionNo }} </n-descriptions-item>
        </n-descriptions>
        <n-form
            ref="formRef"
            class="mt-15px"
            :model="form"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="110"
            :show-feedback="false"
        >
            <n-grid :cols="24" :y-gap="10" :x-gap="10">
                <n-form-item-gi :span="12" label="发放类型" path="distributeType">
                    <n-select
                        v-model:value="form.distributeType"
                        :options="distributeTypeOptions"
                        placeholder="请选择发放类型"
                        clearable
                        :consistent-menu-width="false"
                        :virtual-scroll="false"
                        @update:value="handleDistributeTypeChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件形式" path="fileForm">
                    <n-select
                        v-model:value="form.fileForm"
                        :options="fileFormOptions"
                        placeholder="请选择文件形式"
                        clearable
                        :consistent-menu-width="false"
                        :virtual-scroll="false"
                        @update:value="handleFileFormChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="文件权限" path="filePermission">
                    <n-select
                        v-model:value="form.filePermission"
                        :options="filePermissionOptions"
                        placeholder="请选择文件权限"
                        clearable
                        :consistent-menu-width="false"
                        :virtual-scroll="false"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="发放原因" path="reason">
                    <n-select
                        v-model:value="form.reason"
                        :options="reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                        :consistent-menu-width="false"
                        :virtual-scroll="false"
                        @update:value="onReasonChange"
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="form.reason === '其他'" :span="24" label="其他原因" path="otherReason">
                    <n-input v-model:value="form.otherReason" maxlength="50" placeholder="请输入其他原因" show-count />
                </n-form-item-gi>
                <n-form-item-gi :span="24" label="希望发放日期">
                    <n-date-picker
                        v-model:value="form.wishDistributeDate"
                        type="date"
                        placeholder="请选择希望发放日期"
                        clearable
                        format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';
import { FormRules } from 'naive-ui';

const store = useStore();

const props = withDefaults(
    defineProps<{
        row: any;
        type: number;
        fileType?: number;
    }>(),
    {
        fileType: 1
    }
);
const formRef = ref();
const data = computed(() => {
    return {
        applicant: store.userInfo.nickname,
        applyDate: dayjs().format('YYYY-MM-DD'),
        name: props.row.name,
        versionNo: props.row.versionNo || props.row.version,
        fileCategory: props.row.docCategoryName || props.row.docType,
        number: props.row.no || props.row.number
    };
});
const rules: FormRules = {
    distributeType: { required: true, message: '请选择发放类型', trigger: ['change', 'blur'], type: 'number' },
    fileForm: { required: true, message: '请选择发放形式', trigger: ['change', 'blur'], type: 'number' },
    filePermission: { required: true, message: '请选择发放权限', trigger: ['change', 'blur'], type: 'number' },
    reason: { required: true, message: '请选择发放原因', trigger: ['change', 'blur'] },
    otherReason: {
        required: true,
        message: '请输入其他原因',
        trigger: ['change', 'blur'],
        validator: (rule: any, value: any) => {
            if (form.reason === '其他' && !value) {
                return new Error('请输入其他原因');
            }
            return true;
        }
    }
};

const form = reactive({
    distributeType: null as number | null,
    fileForm: null as number | null,
    filePermission: null as number | null,
    reason: null as string | null,
    otherReason: null as string | null,
    wishDistributeDate: null as number | null,
    distributeList: []
});

// 发放原因
const reasonOptions = [
    { label: '新员工入职', value: '新员工入职' },
    { label: '岗位/职责调整', value: '岗位/职责调整' },
    { label: '文件版本更新', value: '文件版本更新' },
    { label: '新增业务/流程实施', value: '新增业务/流程实施' },
    { label: '跨部门协作需求', value: '跨部门协作需求' },
    { label: '其他', value: '其他' }
];
const onReasonChange = (val: string) => {
    if (val !== '其他') {
        form.otherReason = null;
    }
};

// 发放类型选项
// 权限说明：
// - 管理员：可以选择内部发放和外部发放
// - 普通用户：
//   - 查阅操作：只能选择内部发放
//   - 下载操作：只能选择外部发放
const distributeTypeOptions = computed(() => {
    if (isAdmin.value) {
        // 管理员可以选择所有发放类型
        return [
            { label: '内部发放', value: 1 },
            { label: '外部发放', value: 2 }
        ];
    } else {
        // 普通用户根据操作类型限制
        if (props.type === 1) {
            // 查阅操作：只能内部发放
            return [{ label: '内部发放', value: 1 }];
        } else if (props.type === 2) {
            // 下载操作：只能外部发放
            return [{ label: '外部发放', value: 2 }];
        }
    }
    return [];
});

// 检查用户是否为管理员
const isAdmin = ref(false);

// 在组件挂载时检查管理员权限并设置默认发放类型
onMounted(async () => {
    try {
        const adminCheck = await $utils.filePermissionUtils.checkIsAdmin();
        isAdmin.value = adminCheck.isAdmin;

        // 如果不是管理员，根据操作类型自动设置发放类型
        if (!isAdmin.value) {
            if (props.type === 1) {
                // 查阅操作：自动设置为内部发放
                form.distributeType = 1;
            } else if (props.type === 2) {
                // 下载操作：自动设置为外部发放
                form.distributeType = 2;
            }
        }
    } catch (error) {
        console.error('检查管理员权限失败:', error);
        isAdmin.value = false;
    }
});

// 文件形式选项
// 权限说明：
// - 内部发放：管理员可以选择电子文件和纸质文件，普通用户只能选择电子文件
// - 外部发放：只有电子文件
const fileFormOptions = computed(() => {
    if (form.distributeType === 1) {
        // 内部发放
        if (isAdmin.value) {
            return [
                { label: '电子文件', value: 1 },
                { label: '纸质文件', value: 2 }
            ];
        } else {
            return [{ label: '电子文件', value: 1 }];
        }
    } else if (form.distributeType === 2) {
        // 外部发放：只有电子文件
        return [{ label: '电子文件', value: 1 }];
    }
    return [];
});

const handleFileFormChange = () => {
    form.filePermission = null;
};

// 发放类型变化时清空相关字段
const handleDistributeTypeChange = () => {
    form.fileForm = null;
    form.filePermission = null;
};

// 文件权限选项
// 权限说明：
// - 内部发放：
//   - 电子文件：管理员可选查阅、查阅/下载，普通用户只能查阅
//   - 纸质文件：只有一次下载权限
// - 外部发放：
//   - 电子文件：只有一次下载权限
const filePermissionOptions = computed(() => {
    if (form.distributeType === 1) {
        // 内部发放
        if (form.fileForm === 1) {
            // 电子文件
            if (isAdmin.value) {
                return [
                    { label: '查阅', value: 1 },
                    { label: '查阅/下载', value: 2 }
                ];
            } else {
                return [{ label: '查阅', value: 1 }];
            }
        } else if (form.fileForm === 2) {
            // 纸质文件：只有一次下载
            return [{ label: '一次下载', value: 3 }];
        }
    } else if (form.distributeType === 2) {
        // 外部发放：只有电子文件一次下载
        return [{ label: '一次下载', value: 3 }];
    }
    return [];
});

const submit = async () => {
    try {
        await formRef.value?.validate();
    } catch (err: any) {
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }
    // 组装数据
    const distributeList = [
        {
            fileId: props.row.id,
            fileName: props.row.name,
            number: props.row.no || props.row.number,
            version: props.row.versionNo || props.row.version,
            permissions: [
                {
                    fileForm: form.fileForm,
                    filePermission: form.filePermission,
                    receiver: '',
                    receivedBy: [
                        {
                            userId: store.userInfo.id,
                            userName: store.userInfo.nickname
                        }
                    ]
                }
            ]
        }
    ];
    const result = {
        applicant: data.value.applicant,
        applyDate: Date.now(),
        distributeType: form.distributeType,
        typeDictNodeId: props.row.docCategoryId || props.row.typeDictionaryNodeId,
        category: props.row.docCategoryName || props.row.docType,
        reason: form.reason,
        otherReason: form.otherReason,
        wishDistributeDate: form.wishDistributeDate,
        distributeList,
        fileType: props.fileType ?? ''
    };
    const formData = JSON.stringify({
        businessId: 'FILE_GRANT',
        version: '1.0.0',
        data: result
    });
    await $hooks.useApprovalProcess('FILE_GRANT', formData);
    window.$message.success('发放申请已提交');
};
</script>

<style scoped></style>
